# AdMesh Protocol - Environment Management

.PHONY: dev test prod install clean help

# Default environment
ENV ?= development

# Python virtual environment
VENV_DIR = venv
PYTHON = $(VENV_DIR)/bin/python
PIP = $(VENV_DIR)/bin/pip

help:
	@echo "AdMesh Protocol - Available Commands:"
	@echo ""
	@echo "Environment Commands:"
	@echo "  make dev     - Run development environment"
	@echo "  make test    - Run test environment (prod DB + localhost API)"
	@echo "  make prod    - Run production environment"
	@echo ""
	@echo "Setup Commands:"
	@echo "  make install - Install dependencies"
	@echo "  make clean   - Clean up temporary files"
	@echo ""
	@echo "Direct Commands:"
	@echo "  ENV=development uvicorn api.main:app --reload --host 127.0.0.1 --port 8000 --env-file .env.dev"
	@echo "  ENV=test uvicorn api.main:app --reload --host 0.0.0.0 --port 8000 --env-file .env.test"
	@echo "  ENV=production uvicorn api.main:app --host 0.0.0.0 --port 8080 --env-file .env.production"

dev:
	@echo "🚀 Starting AdMesh Protocol in DEVELOPMENT mode..."
	@echo "📊 Database: Development (admesh-dev)"
	@echo "🌐 API: http://127.0.0.1:8000"
	ENV=development uvicorn api.main:app --reload --host 127.0.0.1 --port 8000 --env-file .env.dev

test:
	@echo "🧪 Starting AdMesh Protocol in TEST mode..."
	@echo "📊 Database: Production (admesh-9560c)"
	@echo "🌐 API: http://127.0.0.1:8000"
	ENV=test uvicorn api.main:app --reload --host 127.0.0.1 --port 8000 --env-file .env.test

prod:
	@echo "🏭 Starting AdMesh Protocol in PRODUCTION mode..."
	@echo "📊 Database: Production (admesh-9560c)"
	@echo "🌐 API: https://api.useadmesh.com"
	ENV=production uvicorn api.main:app --host 0.0.0.0 --port 8080 --env-file .env.production

install:
	@echo "📦 Installing dependencies..."
	pip install -r requirements.txt

clean:
	@echo "🧹 Cleaning up..."
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
