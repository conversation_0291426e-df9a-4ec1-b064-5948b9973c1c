# AdMesh Protocol - Backend Development Guide

The AdMesh Protocol is a FastAPI-based backend service that provides the core API functionality for the AdMesh platform.

## 🏗️ Architecture

- **Framework:** FastAPI with Python 3.9+
- **Database:** Google Firestore
- **Authentication:** Firebase Auth
- **API Documentation:** Automatic OpenAPI/Swagger
- **Deployment:** Google Cloud Run

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- pip
- Virtual environment (recommended)

### Installation
```bash
cd admesh-protocol

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Development
```bash
# Development mode
./run_dev.sh

# Test mode (production data, localhost API)
./run_test.sh

# Production mode
./run_prod.sh
```

## 🌍 Environment Configuration

### Environment Management
The backend uses a sophisticated environment management system with automatic switching:

```bash
# Switch environments
python scripts/switch_env.py dev
python scripts/switch_env.py test
python scripts/switch_env.py prod

# Check current environment
python -c "from config.config_manager import get_environment; print(get_environment())"
```

### Environment Files
```
.env.dev         # Development configuration
.env.test        # Test environment configuration
.env.production  # Production configuration
.env             # Active environment (auto-generated)
```

### Configuration Classes
```
config/
├── base.py              # Base configuration class
├── development.py       # Development settings
├── test.py             # Test environment settings
├── production.py       # Production settings
└── config_manager.py   # Configuration manager
```

## 📁 Project Structure

```
admesh-protocol/
├── api/                    # FastAPI application
│   ├── main.py            # Application entry point
│   ├── routes/            # API route handlers
│   ├── models/            # Pydantic models
│   └── middleware/        # Custom middleware
├── auth/                  # Authentication utilities
├── click/                 # Click tracking functionality
├── config/                # Configuration management
├── firebase/              # Firebase service account keys
├── functions/             # Cloud Functions
├── offers/                # Offer management
├── utils/                 # Utility functions
├── scripts/               # Utility scripts
├── tests/                 # Test files
└── requirements.txt       # Python dependencies
```

## 🔧 Development Workflow

### 1. Start Development Server
```bash
./run_dev.sh
```
- Runs on `http://127.0.0.1:8000`
- Auto-reload enabled
- Debug logging enabled
- Uses development Firebase project

### 2. Environment-Specific Development
```bash
# Test with production data
./run_test.sh

# Production configuration
./run_prod.sh
```

### 3. Manual Server Control
```bash
# Development
uvicorn api.main:app --reload --host 127.0.0.1 --port 8000

# Production
uvicorn api.main:app --host 0.0.0.0 --port 8000

# With specific environment
uvicorn api.main:app --reload --env-file .env.dev
```

## 🔥 Firebase Integration

### Configuration
Firebase configuration is handled automatically based on environment:

- **Development:** Uses `admesh-dev` project with `dev-serviceAccountKey.json`
- **Test:** Uses `admesh-9560c` project with `serviceAccountKey.json`
- **Production:** Uses `admesh-9560c` project with Cloud Run secrets

### Firestore Usage
```python
from firebase_admin import firestore
from config.config_manager import get_config

# Get Firestore client
db = firestore.client()

# Read data
doc_ref = db.collection('users').document('user_id')
doc = doc_ref.get()
if doc.exists:
    data = doc.to_dict()

# Write data
doc_ref.set({
    'name': 'John Doe',
    'email': '<EMAIL>'
})

# Query data
users = db.collection('users').where('active', '==', True).stream()
for user in users:
    print(f'{user.id} => {user.to_dict()}')
```

### Authentication
```python
from firebase_admin import auth
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def verify_token(token: str = Depends(security)):
    try:
        decoded_token = auth.verify_id_token(token.credentials)
        return decoded_token
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid token")
```

## 🌐 API Development

### Route Structure
```python
from fastapi import APIRouter, Depends
from pydantic import BaseModel

router = APIRouter(prefix="/api/v1", tags=["example"])

class ExampleRequest(BaseModel):
    name: str
    email: str

class ExampleResponse(BaseModel):
    id: str
    message: str

@router.post("/example", response_model=ExampleResponse)
async def create_example(
    request: ExampleRequest,
    user: dict = Depends(verify_token)
):
    # Process request
    return ExampleResponse(
        id="example_id",
        message=f"Hello {request.name}"
    )
```

### Error Handling
```python
from fastapi import HTTPException
from utils.logger import get_logger

logger = get_logger(__name__)

@router.get("/example/{item_id}")
async def get_example(item_id: str):
    try:
        # Your logic here
        pass
    except ValueError as e:
        logger.error(f"Invalid item_id: {item_id}, error: {e}")
        raise HTTPException(status_code=400, detail="Invalid item ID")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
```

### Middleware
```python
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import time

class TimingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response
```

## 🔒 Authentication & Authorization

### JWT Token Verification
```python
from functools import wraps
from firebase_admin import auth

def require_auth(f):
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            raise HTTPException(status_code=401, detail="No token provided")
        
        try:
            # Remove 'Bearer ' prefix
            token = token.replace('Bearer ', '')
            decoded_token = auth.verify_id_token(token)
            request.user = decoded_token
        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        return await f(*args, **kwargs)
    return decorated_function
```

### Role-Based Access Control
```python
def require_role(required_role: str):
    def decorator(f):
        @wraps(f)
        async def decorated_function(*args, **kwargs):
            user = getattr(request, 'user', None)
            if not user:
                raise HTTPException(status_code=401, detail="Authentication required")
            
            user_role = user.get('role', 'user')
            if user_role != required_role:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
            
            return await f(*args, **kwargs)
        return decorated_function
    return decorator
```

## 📊 Database Operations

### Firestore Patterns
```python
from typing import List, Optional
from datetime import datetime

class UserService:
    def __init__(self):
        self.db = firestore.client()
        self.collection = self.db.collection('users')
    
    async def create_user(self, user_data: dict) -> str:
        user_data['created_at'] = datetime.utcnow()
        doc_ref = self.collection.add(user_data)
        return doc_ref[1].id
    
    async def get_user(self, user_id: str) -> Optional[dict]:
        doc = self.collection.document(user_id).get()
        return doc.to_dict() if doc.exists else None
    
    async def update_user(self, user_id: str, updates: dict) -> bool:
        updates['updated_at'] = datetime.utcnow()
        self.collection.document(user_id).update(updates)
        return True
    
    async def list_users(self, limit: int = 10) -> List[dict]:
        docs = self.collection.limit(limit).stream()
        return [{'id': doc.id, **doc.to_dict()} for doc in docs]
```

### Transaction Handling
```python
from google.cloud.firestore import transactional

@transactional
def update_user_balance(transaction, user_id: str, amount: float):
    user_ref = db.collection('users').document(user_id)
    user_doc = user_ref.get(transaction=transaction)
    
    if not user_doc.exists:
        raise ValueError("User not found")
    
    current_balance = user_doc.to_dict().get('balance', 0)
    new_balance = current_balance + amount
    
    transaction.update(user_ref, {'balance': new_balance})
    return new_balance

# Usage
transaction = db.transaction()
new_balance = update_user_balance(transaction, 'user_id', 100.0)
```

## 🧪 Testing

### Unit Tests
```python
import pytest
from fastapi.testclient import TestClient
from api.main import app

client = TestClient(app)

def test_health_endpoint():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

def test_create_user():
    user_data = {
        "name": "Test User",
        "email": "<EMAIL>"
    }
    response = client.post("/api/v1/users", json=user_data)
    assert response.status_code == 201
    assert response.json()["name"] == user_data["name"]
```

### Integration Tests
```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_users.py

# Run with coverage
python -m pytest --cov=api tests/
```

### API Testing
```bash
# Test health endpoint
curl http://127.0.0.1:8000/health

# Test with authentication
curl -H "Authorization: Bearer your-token" \
     -H "Content-Type: application/json" \
     -d '{"name": "Test"}' \
     http://127.0.0.1:8000/api/v1/users
```

## 📈 Performance Optimization

### Async Operations
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def process_multiple_requests(requests: List[dict]):
    # Process requests concurrently
    tasks = [process_single_request(req) for req in requests]
    results = await asyncio.gather(*tasks)
    return results

async def process_single_request(request: dict):
    # Your async processing logic
    await asyncio.sleep(0.1)  # Simulate async operation
    return {"processed": True, "data": request}
```

### Caching
```python
from functools import lru_cache
import redis

# In-memory caching
@lru_cache(maxsize=128)
def get_cached_data(key: str):
    # Expensive operation
    return expensive_computation(key)

# Redis caching
redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(key: str, data: dict, ttl: int = 3600):
    redis_client.setex(key, ttl, json.dumps(data))

def get_cached_result(key: str) -> Optional[dict]:
    cached = redis_client.get(key)
    return json.loads(cached) if cached else None
```

## 🚨 Troubleshooting

### Common Issues

#### Environment Not Switching
```bash
# Check current environment
grep "ENV=" .env

# Manually switch
python scripts/switch_env.py dev

# Verify configuration
python -c "from config.config_manager import get_config; print(get_config().environment)"
```

#### Firebase Connection Issues
```bash
# Check credentials file exists
ls -la firebase/

# Verify credentials
python -c "
import firebase_admin
from firebase_admin import credentials
cred = credentials.Certificate('./firebase/serviceAccountKey.json')
print('Firebase credentials valid')
"
```

#### Port Already in Use
```bash
# Find process using port 8000
lsof -i :8000

# Kill process
kill -9 <PID>
```

#### Dependencies Issues
```bash
# Reinstall dependencies
pip install -r requirements.txt --force-reinstall

# Check for conflicts
pip check
```

### Debug Mode
Enable detailed logging:
```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
./run_dev.sh
```

## 🔄 Deployment

### Google Cloud Run
```bash
# Build and deploy
gcloud run deploy admesh-api \
  --source . \
  --platform managed \
  --region us-central1 \
  --project admesh-9560c \
  --set-env-vars ENV=production
```

### Docker
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 📚 Additional Resources

- **FastAPI Documentation:** https://fastapi.tiangolo.com
- **Firebase Admin SDK:** https://firebase.google.com/docs/admin/setup
- **Google Cloud Firestore:** https://cloud.google.com/firestore/docs
- **Pydantic:** https://pydantic-docs.helpmanual.io
- **Uvicorn:** https://www.uvicorn.org
